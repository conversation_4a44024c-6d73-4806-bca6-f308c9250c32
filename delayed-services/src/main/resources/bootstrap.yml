server:
  port: 7007
spring:
  application:
    name: delayed-services #服务名称 必须要有
  main:
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: 172.31.16.71:8848,172.31.59.233:8848 #服务注册中心地址
        namespace: b0dca7cf-f37b-47c0-97d1-0e25ad689422 #命名空间
      config:
        server-addr: 172.31.16.71:8848,172.31.59.233:8848 #配置中心地址
        file-extension: yaml #指定yaml格式的配置
        group: DEFAULT_GROUP
        namespace: b0dca7cf-f37b-47c0-97d1-0e25ad689422

