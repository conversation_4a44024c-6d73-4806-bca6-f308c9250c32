# 商品查询临时表优化方案

## 背景

在 `queryProductGroupBySku` 方法中，`skuCacheService.matchSkus(request.getSku())` 在极端情况下会返回上万条数据，这些数据通过 `IN` 子句进行查询时会严重影响性能。

## 问题分析

1. **性能瓶颈**：当匹配的SKU数量达到上万条时，SQL中的 `IN` 子句会变得非常长
2. **内存占用**：大量SKU数据在内存中传递和处理
3. **查询超时**：复杂的 `IN` 查询可能导致数据库查询超时

## 解决方案

利用 MySQL 8 的临时表特性，将大量SKU数据存储在临时表中，通过 `EXISTS` 子查询替代 `IN` 子句，显著提升查询性能。

## 技术实现

### 1. 核心组件

#### 1.1 Mapper接口扩展
- `createTempSkuTable()`: 创建临时表
- `insertSkusToTempTable()`: 批量插入SKU数据
- `insertRemarksToTempTable()`: 批量插入备注数据
- `queryProductGroupBySkuWithTempTable()`: 使用临时表的优化查询
- `dropTempSkuTable()`: 清理临时表

#### 1.2 临时表结构
```sql
CREATE TEMPORARY TABLE temp_sku_search (
    sku_value VARCHAR(500) NOT NULL,
    search_type ENUM('SKU', 'REMARK') NOT NULL DEFAULT 'SKU',
    PRIMARY KEY (sku_value, search_type),
    INDEX idx_sku_value (sku_value),
    INDEX idx_search_type (search_type)
) ENGINE=MEMORY
```

#### 1.3 优化查询SQL
```sql
-- 原查询使用IN子句
WHERE kp.sku_indexed IN (sku1, sku2, ..., sku10000)

-- 优化后使用EXISTS子查询
WHERE EXISTS (SELECT 1 FROM temp_sku_search ts 
              WHERE ts.sku_value = kp.sku_indexed AND ts.search_type = 'SKU')
```

### 2. 配置管理

#### 2.1 配置类 `TempTableConfig`
```yaml
knet:
  goods:
    temp-table:
      enabled: true          # 是否启用临时表优化
      sku-threshold: 1000    # 启用临时表的SKU数量阈值
      batch-size: 5000       # 批量插入的批次大小
      engine: MEMORY         # 临时表引擎类型
      auto-cleanup: true     # 是否自动清理临时表
```

#### 2.2 工具类 `TempTableManager`
- 统一管理临时表的生命周期
- 提供分批插入功能
- 自动清理资源
- 配置信息管理

### 3. 智能切换策略

系统会根据匹配的SKU数量自动选择查询策略：

- **SKU数量 ≤ 1000**：使用传统 `IN` 查询
- **SKU数量 > 1000**：使用临时表优化查询

## 性能优势

### 1. 查询性能提升
- **传统方式**：`IN` 子句包含上万个值，查询计划复杂
- **优化方式**：`EXISTS` 子查询配合索引，查询效率显著提升

### 2. 内存使用优化
- **传统方式**：大量SKU数据在应用内存中传递
- **优化方式**：数据存储在数据库临时表中，减少应用内存占用

### 3. 可扩展性
- 支持配置化管理
- 支持不同引擎类型（MEMORY/InnoDB）
- 支持批量处理大数据集

## 使用示例

### 1. 基本使用
```java
// 系统会自动根据SKU数量选择优化策略
IPage<ProductBySkuDtoResp> result = knetProductService.queryProductGroupBySku(request);
```

### 2. 配置调整
```yaml
# 调整阈值，当SKU数量超过500时使用临时表
knet.goods.temp-table.sku-threshold: 500

# 调整批次大小，每次插入3000条数据
knet.goods.temp-table.batch-size: 3000
```

### 3. 性能测试
```java
@Test
public void testTempTableOptimization() {
    // 使用测试类进行性能对比
    // 详见：KnetProductServiceTempTableTest
}
```

## 注意事项

### 1. 数据库兼容性
- 需要 MySQL 8.0+ 版本
- 临时表功能依赖数据库连接会话

### 2. 内存限制
- MEMORY引擎有大小限制，超大数据集建议使用InnoDB引擎
- 可通过配置调整引擎类型

### 3. 异常处理
- 临时表创建失败时自动回退到传统查询
- 确保临时表在异常情况下也能正确清理

### 4. 监控建议
- 监控临时表使用频率
- 监控查询性能改善情况
- 关注内存使用变化

## 扩展计划

1. **支持更多查询场景**：将临时表优化扩展到其他大数据量查询
2. **性能监控**：添加查询性能指标收集
3. **缓存优化**：结合Redis缓存进一步优化查询性能
4. **分库分表支持**：在分库分表环境下的临时表优化策略

## 总结

通过引入MySQL 8临时表优化，成功解决了大量SKU查询的性能问题，提升了系统的可扩展性和稳定性。该方案具有以下特点：

- ✅ **性能显著提升**：解决了上万SKU查询的性能瓶颈
- ✅ **智能切换**：根据数据量自动选择最优查询策略  
- ✅ **配置灵活**：支持多种配置参数调整
- ✅ **异常安全**：完善的异常处理和资源清理机制
- ✅ **向后兼容**：不影响现有功能，平滑升级
