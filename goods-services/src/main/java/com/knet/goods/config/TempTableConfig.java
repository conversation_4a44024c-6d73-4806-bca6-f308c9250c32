package com.knet.goods.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/7/8 15:45
 * @description: 临时表优化配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "knet.goods.temp-table")
public class TempTableConfig {

    /**
     * 临时表策略枚举
     */
    public enum Strategy {
        /**
         * 单表策略：SKU和REMARK存储在同一张表中，通过search_type字段区分
         */
        SINGLE_TABLE,

        /**
         * 分表策略：SKU和REMARK分别存储在不同的表中
         */
        SEPARATE_TABLES
    }

    /**
     * 临时表策略
     * SINGLE_TABLE: 单表策略（默认）
     * SEPARATE_TABLES: 分表策略（推荐）
     */
    private Strategy strategy = Strategy.SEPARATE_TABLES;

    /**
     * 启用临时表优化的SKU数量阈值
     * 当匹配的SKU数量超过此值时，使用临时表优化查询
     */
    private int skuThreshold = 1000;

    /**
     * 批量插入临时表的批次大小
     */
    private int batchSize = 5000;

    /**
     * 是否启用临时表优化
     */
    private boolean enabled = true;

    /**
     * 临时表引擎类型
     * MEMORY: 内存引擎，速度快但有大小限制
     * InnoDB: 磁盘引擎，无大小限制但速度相对较慢
     */
    private String engine = "MEMORY";

    /**
     * 是否在查询完成后立即清理临时表
     */
    private boolean autoCleanup = true;

    /**
     * SKU临时表名称（分表策略使用）
     */
    private String skuTableName = "temp_sku_list";

    /**
     * 备注临时表名称（分表策略使用）
     */
    private String remarkTableName = "temp_remark_list";

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = true;
}
