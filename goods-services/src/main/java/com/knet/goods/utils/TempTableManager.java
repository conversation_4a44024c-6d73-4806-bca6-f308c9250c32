package com.knet.goods.utils;

import cn.hutool.core.collection.CollUtil;
import com.knet.goods.config.TempTableConfig;
import com.knet.goods.mapper.KnetProductMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/7/8 16:00
 * @description: 临时表管理工具类
 */
@Slf4j
@Component
public class TempTableManager {

    @Resource
    private KnetProductMapper knetProductMapper;

    @Resource
    private TempTableConfig tempTableConfig;

    /**
     * 创建并填充临时表
     *
     * @param skus    SKU集合
     * @param remarks 备注集合
     * @return 是否成功创建并填充
     */
    public boolean createAndPopulateTempTable(Set<String> skus, Set<String> remarks) {
        try {
            // 创建临时表
            knetProductMapper.createTempSkuTable();
            log.debug("临时表创建成功");

            // 批量插入SKU数据
            if (CollUtil.isNotEmpty(skus)) {
                insertSkusInBatches(new ArrayList<>(skus));
                log.debug("SKU数据插入完成，数量: {}", skus.size());
            }

            // 批量插入备注数据
            if (CollUtil.isNotEmpty(remarks)) {
                insertRemarksInBatches(new ArrayList<>(remarks));
                log.debug("备注数据插入完成，数量: {}", remarks.size());
            }

            return true;
        } catch (Exception e) {
            log.error("创建或填充临时表失败", e);
            // 尝试清理临时表
            cleanupTempTable();
            return false;
        }
    }

    /**
     * 分批插入SKU数据
     *
     * @param skuList SKU列表
     */
    private void insertSkusInBatches(List<String> skuList) {
        int batchSize = tempTableConfig.getBatchSize();
        for (int i = 0; i < skuList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skuList.size());
            List<String> batch = skuList.subList(i, endIndex);
            knetProductMapper.insertSkusToTempTable(batch);
            log.debug("插入SKU批次: {}-{}", i, endIndex - 1);
        }
    }

    /**
     * 分批插入备注数据
     *
     * @param remarkList 备注列表
     */
    private void insertRemarksInBatches(List<String> remarkList) {
        int batchSize = tempTableConfig.getBatchSize();
        for (int i = 0; i < remarkList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, remarkList.size());
            List<String> batch = remarkList.subList(i, endIndex);
            knetProductMapper.insertRemarksToTempTable(batch);
            log.debug("插入备注批次: {}-{}", i, endIndex - 1);
        }
    }

    /**
     * 清理临时表
     */
    public void cleanupTempTable() {
        if (tempTableConfig.isAutoCleanup()) {
            try {
                knetProductMapper.dropTempSkuTable();
                log.debug("临时表清理成功");
            } catch (Exception e) {
                log.warn("清理临时表失败", e);
            }
        }
    }

    /**
     * 判断是否应该使用临时表优化
     *
     * @param skuCount    SKU数量
     * @param remarkCount 备注数量
     * @return 是否使用临时表
     */
    public boolean shouldUseTempTable(int skuCount, int remarkCount) {
        return tempTableConfig.isEnabled() && 
               (skuCount + remarkCount) > tempTableConfig.getSkuThreshold();
    }

    /**
     * 获取临时表配置信息
     *
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return String.format("临时表配置 - 启用: %s, 阈值: %d, 批次大小: %d, 引擎: %s, 自动清理: %s",
                tempTableConfig.isEnabled(),
                tempTableConfig.getSkuThreshold(),
                tempTableConfig.getBatchSize(),
                tempTableConfig.getEngine(),
                tempTableConfig.isAutoCleanup());
    }
}
