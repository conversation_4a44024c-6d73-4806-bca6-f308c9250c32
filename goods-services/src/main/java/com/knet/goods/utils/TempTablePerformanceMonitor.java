package com.knet.goods.utils;

import com.knet.goods.config.TempTableConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2025/7/8 18:00
 * @description: 临时表性能监控工具
 */
@Slf4j
@Component
public class TempTablePerformanceMonitor {

    @Resource
    private TempTableConfig tempTableConfig;

    // 性能统计数据
    private final ConcurrentHashMap<String, PerformanceStats> performanceStats = new ConcurrentHashMap<>();
    
    /**
     * 性能统计数据结构
     */
    public static class PerformanceStats {
        private final AtomicLong totalExecutions = new AtomicLong(0);
        private final AtomicLong totalTime = new AtomicLong(0);
        private final AtomicLong minTime = new AtomicLong(Long.MAX_VALUE);
        private final AtomicLong maxTime = new AtomicLong(0);
        
        public void recordExecution(long executionTime) {
            totalExecutions.incrementAndGet();
            totalTime.addAndGet(executionTime);
            
            // 更新最小时间
            long currentMin = minTime.get();
            while (executionTime < currentMin && !minTime.compareAndSet(currentMin, executionTime)) {
                currentMin = minTime.get();
            }
            
            // 更新最大时间
            long currentMax = maxTime.get();
            while (executionTime > currentMax && !maxTime.compareAndSet(currentMax, executionTime)) {
                currentMax = maxTime.get();
            }
        }
        
        public long getTotalExecutions() { return totalExecutions.get(); }
        public long getTotalTime() { return totalTime.get(); }
        public long getMinTime() { return minTime.get() == Long.MAX_VALUE ? 0 : minTime.get(); }
        public long getMaxTime() { return maxTime.get(); }
        public double getAverageTime() { 
            long executions = totalExecutions.get();
            return executions > 0 ? (double) totalTime.get() / executions : 0;
        }
    }

    /**
     * 开始性能监控
     * 
     * @param operationType 操作类型
     * @return StopWatch实例
     */
    public StopWatch startMonitoring(String operationType) {
        if (!tempTableConfig.isPerformanceMonitorEnabled()) {
            return null;
        }
        
        StopWatch stopWatch = new StopWatch(operationType);
        stopWatch.start(operationType);
        return stopWatch;
    }

    /**
     * 结束性能监控并记录数据
     * 
     * @param stopWatch StopWatch实例
     * @param dataSize 数据量大小
     */
    public void endMonitoring(StopWatch stopWatch, int dataSize) {
        if (stopWatch == null || !tempTableConfig.isPerformanceMonitorEnabled()) {
            return;
        }
        
        stopWatch.stop();
        long executionTime = stopWatch.getTotalTimeMillis();
        String operationType = stopWatch.getId();
        
        // 记录性能数据
        String key = generateStatsKey(operationType, tempTableConfig.getStrategy());
        performanceStats.computeIfAbsent(key, k -> new PerformanceStats())
                       .recordExecution(executionTime);
        
        // 输出详细日志
        log.info("性能监控 - 操作: {}, 策略: {}, 数据量: {}, 耗时: {} ms", 
                operationType, tempTableConfig.getStrategy(), dataSize, executionTime);
        
        // 如果执行时间过长，输出警告
        if (executionTime > 5000) { // 超过5秒
            log.warn("⚠️ 性能警告 - 操作: {}, 策略: {}, 数据量: {}, 耗时: {} ms (超过5秒)", 
                    operationType, tempTableConfig.getStrategy(), dataSize, executionTime);
        }
    }

    /**
     * 生成统计键
     */
    private String generateStatsKey(String operationType, TempTableConfig.Strategy strategy) {
        return operationType + "_" + strategy.name();
    }

    /**
     * 获取性能统计报告
     * 
     * @return 性能报告字符串
     */
    public String getPerformanceReport() {
        if (!tempTableConfig.isPerformanceMonitorEnabled()) {
            return "性能监控未启用";
        }
        
        StringBuilder report = new StringBuilder();
        report.append("\n========== 临时表性能统计报告 ==========\n");
        
        performanceStats.forEach((key, stats) -> {
            report.append(String.format(
                "操作: %s\n" +
                "  执行次数: %d\n" +
                "  总耗时: %d ms\n" +
                "  平均耗时: %.2f ms\n" +
                "  最小耗时: %d ms\n" +
                "  最大耗时: %d ms\n" +
                "----------------------------------------\n",
                key, stats.getTotalExecutions(), stats.getTotalTime(),
                stats.getAverageTime(), stats.getMinTime(), stats.getMaxTime()
            ));
        });
        
        return report.toString();
    }

    /**
     * 清空性能统计数据
     */
    public void clearStats() {
        performanceStats.clear();
        log.info("性能统计数据已清空");
    }

    /**
     * 获取特定操作的性能统计
     * 
     * @param operationType 操作类型
     * @param strategy 策略类型
     * @return 性能统计数据
     */
    public PerformanceStats getStats(String operationType, TempTableConfig.Strategy strategy) {
        String key = generateStatsKey(operationType, strategy);
        return performanceStats.get(key);
    }

    /**
     * 比较两种策略的性能
     * 
     * @param operationType 操作类型
     * @return 性能对比报告
     */
    public String compareStrategies(String operationType) {
        PerformanceStats singleTableStats = getStats(operationType, TempTableConfig.Strategy.SINGLE_TABLE);
        PerformanceStats separateTablesStats = getStats(operationType, TempTableConfig.Strategy.SEPARATE_TABLES);
        
        if (singleTableStats == null || separateTablesStats == null) {
            return "缺少对比数据，请先执行相关操作";
        }
        
        StringBuilder comparison = new StringBuilder();
        comparison.append(String.format("\n========== %s 策略性能对比 ==========\n", operationType));
        
        comparison.append("单表策略:\n");
        comparison.append(String.format("  平均耗时: %.2f ms\n", singleTableStats.getAverageTime()));
        comparison.append(String.format("  执行次数: %d\n", singleTableStats.getTotalExecutions()));
        
        comparison.append("分表策略:\n");
        comparison.append(String.format("  平均耗时: %.2f ms\n", separateTablesStats.getAverageTime()));
        comparison.append(String.format("  执行次数: %d\n", separateTablesStats.getTotalExecutions()));
        
        double improvement = ((singleTableStats.getAverageTime() - separateTablesStats.getAverageTime()) 
                             / singleTableStats.getAverageTime()) * 100;
        
        if (improvement > 0) {
            comparison.append(String.format("🚀 分表策略性能提升: %.2f%%\n", improvement));
        } else {
            comparison.append(String.format("📉 分表策略性能下降: %.2f%%\n", Math.abs(improvement)));
        }
        
        return comparison.toString();
    }

    /**
     * 监控临时表创建性能
     */
    public void monitorTableCreation(Runnable tableCreationTask, String tableName) {
        if (!tempTableConfig.isPerformanceMonitorEnabled()) {
            tableCreationTask.run();
            return;
        }
        
        StopWatch stopWatch = new StopWatch("CREATE_TABLE_" + tableName);
        stopWatch.start("创建临时表");
        
        try {
            tableCreationTask.run();
        } finally {
            stopWatch.stop();
            long executionTime = stopWatch.getTotalTimeMillis();
            
            log.debug("临时表创建监控 - 表名: {}, 耗时: {} ms", tableName, executionTime);
            
            // 记录到统计数据
            String key = "CREATE_TABLE_" + tableName + "_" + tempTableConfig.getStrategy().name();
            performanceStats.computeIfAbsent(key, k -> new PerformanceStats())
                           .recordExecution(executionTime);
        }
    }

    /**
     * 监控数据插入性能
     */
    public void monitorDataInsertion(Runnable insertionTask, String dataType, int dataSize) {
        if (!tempTableConfig.isPerformanceMonitorEnabled()) {
            insertionTask.run();
            return;
        }
        
        StopWatch stopWatch = new StopWatch("INSERT_" + dataType);
        stopWatch.start("插入数据");
        
        try {
            insertionTask.run();
        } finally {
            stopWatch.stop();
            long executionTime = stopWatch.getTotalTimeMillis();
            
            log.debug("数据插入监控 - 类型: {}, 数量: {}, 耗时: {} ms", dataType, dataSize, executionTime);
            
            // 记录到统计数据
            String key = "INSERT_" + dataType + "_" + tempTableConfig.getStrategy().name();
            performanceStats.computeIfAbsent(key, k -> new PerformanceStats())
                           .recordExecution(executionTime);
        }
    }
}
