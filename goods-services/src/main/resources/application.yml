spring:
  profiles:
    active: prd
  config:
    import: optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  mvc:
    servlet:
      path: /goodServices
  main:
    allow-circular-references: true
#actuator 运维配置信息
management:
  endpoints:
    web:
      exposure:
        include: "health,info"
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  type-aliases-package: com.knet.entity

# 临时表优化配置
knet:
  goods:
    temp-table:
      strategy: SEPARATE_TABLES  # SINGLE_TABLE | SEPARATE_TABLES
      enabled: true
      sku-threshold: 1000
      batch-size: 5000
      engine: MEMORY
      auto-cleanup: true
      sku-table-name: temp_sku_list
      remark-table-name: temp_remark_list
      performance-monitor-enabled: true