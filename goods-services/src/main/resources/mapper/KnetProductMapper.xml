<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.goods.mapper.KnetProductMapper">

    <!--查询商品列表-按sku分组-->
    <select id="queryProductGroupBySku"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="com.knet.goods.model.dto.resp.ProductBySkuDtoResp">
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <!-- skus 集合为空的场景-->
            <if test="(request.skus == null or request.skus.size() == 0) and request.sku != null and request.sku != ''">
                AND (kp.sku LIKE CONCAT(#{request.sku}, '%')
                OR kp.remarks LIKE CONCAT(#{request.sku}, '%'))
            </if>
            <!-- skus 集合不为空的场景-->
            <if test="request.skus != null and request.skus.size() > 0">
                AND (
                kp.sku_indexed IN
                <foreach collection="request.skus" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="request.remarks != null and request.remarks.size() > 0">
                    OR kp.remarks IN
                    <foreach collection="request.remarks" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
        </where>
        GROUP BY
        kp.sku
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        <!-- 计算分页偏移量 -->
        <bind name="offset" value="(request.pageNo - 1) * request.pageSize"/>
        LIMIT #{offset}, #{request.pageSize}
    </select>
    <!--分页查询总页数-->
    <select id="queryProductGroupBySkuCount"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="java.lang.Integer">
        SELECT COUNT( * ) AS total FROM(
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <!-- skus 集合为空的场景-->
            <if test="(request.skus == null or request.skus.size() == 0) and request.sku != null and request.sku != ''">
                AND (kp.sku LIKE CONCAT(#{request.sku}, '%')
                OR kp.remarks LIKE CONCAT(#{request.sku}, '%'))
            </if>
            <!-- skus 集合不为空的场景-->
            <if test="request.skus != null and request.skus.size() > 0">
                AND (
                kp.sku_indexed IN
                <foreach collection="request.skus" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="request.remarks != null and request.remarks.size() > 0">
                    OR kp.remarks IN
                    <foreach collection="request.remarks" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
        </where>
        GROUP BY
        kp.sku
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        ) t
    </select>

    <!--查询当前sku下的商品详情信息-->
    <select id="queryProductDetails" resultType="com.knet.goods.model.dto.resp.ProductSkuSpecPriceDtoResp">
        SELECT
        kp.sku,
        kp.spec,
        kp.mark,
        MAX(kp.price ) AS maxPrice,
        MIN(kp.price ) AS minPrice,
        AVG(kp.price) AS avgPrice,
        SUM(kp.price) AS totalPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        WHERE
        kp.del_flag = 0
        AND kp.price > 0
        AND kp.`status` in ('ON_SALE')
        <if test="request.sku != null and request.sku != ''">
            AND kp.sku = #{request.sku}
        </if>
        <if test="request.account != null and request.account != ''">
            AND kp.source !=#{request.account}
        </if>
        GROUP BY
        kp.spec
    </select>
    <!--查询当前sku下的商品详情信息-价格信息-->
    <select id="queryProductDetailsPriceInfo"
            resultType="com.knet.goods.model.dto.resp.SpecPriceDto">
        SELECT
        kp.spec,
        kp.price AS price,
        COUNT( * ) AS qty
        FROM
        knet_product kp
        WHERE
        kp.del_flag = 0
        AND kp.price > 0
        AND kp.`status` in ('ON_SALE')
        <if test="request.sku != null and request.sku != ''">
            AND kp.sku = #{request.sku}
        </if>
        <if test="request.account != null and request.account != ''">
            AND kp.source !=#{request.account}
        </if>
        GROUP BY
        kp.sku,
        kp.spec,
        kp.price
    </select>

    <!--批量插入-允许部分失败-->
    <insert id="insertIgnoreBatch">
        INSERT IGNORE INTO knet_product
        (one_id, sku, spec, warehouse, status, listing_id, price,creator,source,brand,remarks,mark)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.oneId}
            , #{item.sku}
            , #{item.spec}
            , #{item.warehouse}
            , #{item.status}
            , #{item.listingId}
            , #{item.price}
            , #{item.creator}
            , #{item.source}
            , #{item.brand}
            , #{item.remarks}
            , #{item.mark}
            )
        </foreach>
    </insert>

    <!--更新商品为下架状态-->
    <update id="updateKnetProductForOffSale">
        UPDATE knet_product
        SET `status` = 'OFF_SALE'
        WHERE
        listing_id IN
        <foreach collection="oneIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sku = #{sku}
        AND `status` = 'ON_SALE'
        AND del_flag = 0
    </update>

    <!-- ==================== 分表临时表优化 ==================== -->

    <!--创建SKU临时表-->
    <update id="createTempSkuListTable">
        CREATE TEMPORARY TABLE IF NOT EXISTS temp_sku_list
        (
            sku_value VARCHAR(50) NOT NULL,
            PRIMARY KEY (sku_value)
        ) ENGINE = MEMORY
    </update>

    <!--创建备注临时表-->
    <update id="createTempRemarkListTable">
        CREATE TEMPORARY TABLE IF NOT EXISTS temp_remark_list
        (
            remark_value VARCHAR(200) NOT NULL,
            PRIMARY KEY (remark_value)
        ) ENGINE = MEMORY
    </update>

    <!--批量插入SKU到SKU临时表-->
    <insert id="insertSkusToSkuTable">
        INSERT IGNORE INTO temp_sku_list (sku_value) VALUES
        <foreach collection="skus" item="sku" separator=",">
            (#{sku})
        </foreach>
    </insert>

    <!--批量插入备注到备注临时表-->
    <insert id="insertRemarksToRemarkTable">
        INSERT IGNORE INTO temp_remark_list (remark_value) VALUES
        <foreach collection="remarks" item="remark" separator=",">
            (#{remark})
        </foreach>
    </insert>

    <!--删除SKU临时表-->
    <update id="dropTempSkuListTable">
        DROP TEMPORARY TABLE IF EXISTS temp_sku_list
    </update>

    <!--删除备注临时表-->
    <update id="dropTempRemarkListTable">
        DROP TEMPORARY TABLE IF EXISTS temp_remark_list
    </update>

    <!--查询商品列表-按sku分组-使用分表优化-->
    <select id="queryProductGroupBySkuWithSeparateTables"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="com.knet.goods.model.dto.resp.ProductBySkuDtoResp">
        SELECT
        kp.sku,
        kp.brand,
        MIN(kp.price) AS minPrice,
        MAX(kp.price) AS maxPrice,
        COUNT(*) AS total
        FROM knet_product kp
        WHERE
        kp.del_flag = 0
        AND kp.price > 0
        AND kp.`status` in ('ON_SALE')
        <if test="request.brand != null and request.brand != ''">
            AND kp.brand = #{request.brand}
        </if>
        <!-- 使用分表进行SKU和商品品名 查询 -->
        AND (
        EXISTS (SELECT 1 FROM temp_sku_list ts WHERE ts.sku_value = kp.sku_indexed)
        OR EXISTS (SELECT 1 FROM temp_remark_list tr WHERE tr.remark_value = kp.remarks)
        )
        <if test="request.mark != null and request.mark.name() != ''">
            AND kp.mark = #{request.mark.name}
        </if>
        <if test="request.account != null and request.account != ''">
            AND kp.source !=#{request.account}
        </if>
        GROUP BY
        kp.sku
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        <!-- 计算分页偏移量 -->
        <bind name="offset" value="(request.pageNo - 1) * request.pageSize"/>
        LIMIT #{offset}, #{request.pageSize}
    </select>

    <!--分页查询总页数-使用分表优化-->
    <select id="queryProductGroupBySkuCountWithSeparateTables"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="java.lang.Integer">
        SELECT COUNT( * ) AS total FROM(
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <!-- 使用分表进行SKU和商品品名 查询 -->
            AND (
            EXISTS (SELECT 1 FROM temp_sku_list ts WHERE ts.sku_value = kp.sku_indexed)
            OR EXISTS (SELECT 1 FROM temp_remark_list tr WHERE tr.remark_value = kp.remarks)
            )
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
        </where>
        GROUP BY
        kp.sku
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        ) t
    </select>
</mapper>
