<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.goods.mapper.KnetProductMapper">

    <!--查询商品列表-按sku分组-->
    <select id="queryProductGroupBySku"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="com.knet.goods.model.dto.resp.ProductBySkuDtoResp">
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <!-- skus 集合为空的场景-->
            <if test="(request.skus == null or request.skus.size() == 0) and request.sku != null and request.sku != ''">
                AND (kp.sku LIKE CONCAT(#{request.sku}, '%')
                OR kp.remarks LIKE CONCAT(#{request.sku}, '%'))
            </if>
            <!-- skus 集合不为空的场景-->
            <if test="request.skus != null and request.skus.size() > 0">
                AND (
                kp.sku_indexed IN
                <foreach collection="request.skus" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="request.remarks != null and request.remarks.size() > 0">
                    OR kp.remarks IN
                    <foreach collection="request.remarks" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
        </where>
        GROUP BY
        kp.sku
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        <!-- 计算分页偏移量 -->
        <bind name="offset" value="(request.pageNo - 1) * request.pageSize"/>
        LIMIT #{offset}, #{request.pageSize}
    </select>
    <!--分页查询总页数-->
    <select id="queryProductGroupBySkuCount"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="java.lang.Integer">
        SELECT COUNT( * ) AS total FROM(
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <!-- skus 集合为空的场景-->
            <if test="(request.skus == null or request.skus.size() == 0) and request.sku != null and request.sku != ''">
                AND (kp.sku LIKE CONCAT(#{request.sku}, '%')
                OR kp.remarks LIKE CONCAT(#{request.sku}, '%'))
            </if>
            <!-- skus 集合不为空的场景-->
            <if test="request.skus != null and request.skus.size() > 0">
                AND (
                kp.sku_indexed IN
                <foreach collection="request.skus" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="request.remarks != null and request.remarks.size() > 0">
                    OR kp.remarks IN
                    <foreach collection="request.remarks" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
        </where>
        GROUP BY
        kp.sku
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        ) t
    </select>

    <!--查询当前sku下的商品详情信息-->
    <select id="queryProductDetails" resultType="com.knet.goods.model.dto.resp.ProductSkuSpecPriceDtoResp">
        SELECT
        kp.sku,
        kp.spec,
        kp.mark,
        MAX(kp.price ) AS maxPrice,
        MIN(kp.price ) AS minPrice,
        AVG(kp.price) AS avgPrice,
        SUM(kp.price) AS totalPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        WHERE
        kp.del_flag = 0
        AND kp.price > 0
        AND kp.`status` in ('ON_SALE')
        <if test="request.sku != null and request.sku != ''">
            AND kp.sku = #{request.sku}
        </if>
        <if test="request.account != null and request.account != ''">
            AND kp.source !=#{request.account}
        </if>
        GROUP BY
        kp.spec
    </select>
    <!--查询当前sku下的商品详情信息-价格信息-->
    <select id="queryProductDetailsPriceInfo"
            resultType="com.knet.goods.model.dto.resp.SpecPriceDto">
        SELECT
        kp.spec,
        kp.price AS price,
        COUNT( * ) AS qty
        FROM
        knet_product kp
        WHERE
        kp.del_flag = 0
        AND kp.price > 0
        AND kp.`status` in ('ON_SALE')
        <if test="request.sku != null and request.sku != ''">
            AND kp.sku = #{request.sku}
        </if>
        <if test="request.account != null and request.account != ''">
            AND kp.source !=#{request.account}
        </if>
        GROUP BY
        kp.sku,
        kp.spec,
        kp.price
    </select>

    <!--批量插入-允许部分失败-->
    <insert id="insertIgnoreBatch">
        INSERT IGNORE INTO knet_product
        (one_id, sku, spec, warehouse, status, listing_id, price,creator,source,brand,remarks,mark)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.oneId}
            , #{item.sku}
            , #{item.spec}
            , #{item.warehouse}
            , #{item.status}
            , #{item.listingId}
            , #{item.price}
            , #{item.creator}
            , #{item.source}
            , #{item.brand}
            , #{item.remarks}
            , #{item.mark}
            )
        </foreach>
    </insert>

    <!--更新商品为下架状态-->
    <update id="updateKnetProductForOffSale">
        UPDATE knet_product
        SET `status` = 'OFF_SALE'
        WHERE
        listing_id IN
        <foreach collection="oneIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sku = #{sku}
        AND `status` = 'ON_SALE'
        AND del_flag = 0
    </update>

    <!--创建临时表用于SKU查询优化-->
    <update id="createTempSkuTable">
        CREATE TEMPORARY TABLE IF NOT EXISTS temp_sku_search (
            sku_value VARCHAR(500) NOT NULL,
            search_type ENUM('SKU', 'REMARK') NOT NULL DEFAULT 'SKU',
            PRIMARY KEY (sku_value, search_type),
            INDEX idx_sku_value (sku_value),
            INDEX idx_search_type (search_type)
        ) ENGINE=MEMORY
    </update>

    <!--批量插入SKU到临时表-->
    <insert id="insertSkusToTempTable">
        INSERT IGNORE INTO temp_sku_search (sku_value, search_type) VALUES
        <foreach collection="skus" item="sku" separator=",">
            (#{sku}, 'SKU')
        </foreach>
    </insert>

    <!--批量插入商品备注到临时表-->
    <insert id="insertRemarksToTempTable">
        INSERT IGNORE INTO temp_sku_search (sku_value, search_type) VALUES
        <foreach collection="remarks" item="remark" separator=",">
            (#{remark}, 'REMARK')
        </foreach>
    </insert>

    <!--删除临时表-->
    <update id="dropTempSkuTable">
        DROP TEMPORARY TABLE IF EXISTS temp_sku_search
    </update>

    <!--查询商品列表-按sku分组-使用临时表优化-->
    <select id="queryProductGroupBySkuWithTempTable"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="com.knet.goods.model.dto.resp.ProductBySkuDtoResp">
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <!-- 使用临时表进行SKU和备注查询 -->
            AND (
                EXISTS (SELECT 1 FROM temp_sku_search ts WHERE ts.sku_value = kp.sku_indexed AND ts.search_type = 'SKU')
                OR EXISTS (SELECT 1 FROM temp_sku_search ts WHERE ts.sku_value = kp.remarks AND ts.search_type = 'REMARK')
            )
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
        </where>
        GROUP BY
        kp.sku
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        <!-- 计算分页偏移量 -->
        <bind name="offset" value="(request.pageNo - 1) * request.pageSize"/>
        LIMIT #{offset}, #{request.pageSize}
    </select>

    <!--分页查询总页数-使用临时表优化-->
    <select id="queryProductGroupBySkuCountWithTempTable"
            parameterType="com.knet.goods.model.dto.req.ProductQueryRequest"
            resultType="java.lang.Integer">
        SELECT COUNT( * ) AS total FROM(
        SELECT
        kp.sku,
        kp.remarks,
        kp.brand,
        kp.mark,
        MAX( kp.price ) AS maxPrice,
        MIN( kp.price ) AS minPrice,
        COUNT( * ) AS total
        FROM
        knet_product kp
        <where>
            kp.del_flag = 0
            AND kp.price > 0
            AND kp.`status` in ('ON_SALE')
            <if test="request.brand != null and request.brand != ''">
                AND kp.brand = #{request.brand}
            </if>
            <!-- 使用临时表进行SKU和备注查询 -->
            AND (
                EXISTS (SELECT 1 FROM temp_sku_search ts WHERE ts.sku_value = kp.sku_indexed AND ts.search_type = 'SKU')
                OR EXISTS (SELECT 1 FROM temp_sku_search ts WHERE ts.sku_value = kp.remarks AND ts.search_type = 'REMARK')
            )
            <if test="request.mark != null and request.mark.name() != ''">
                AND kp.mark = #{request.mark.name}
            </if>
            <if test="request.account != null and request.account != ''">
                AND kp.source !=#{request.account}
            </if>
        </where>
        GROUP BY
        kp.sku
        <choose>
            <when test="request.sortBy != null">
                <choose>
                    <when test="request.sortBy.name() == 'UNIT_DESC'">
                        ORDER BY total DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'UNIT_ASC'">
                        ORDER BY total ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_DESC'">
                        ORDER BY maxPrice DESC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'PRICE_ASC'">
                        ORDER BY maxPrice ASC, sku ASC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_DESC'">
                        ORDER BY sku DESC
                    </when>
                    <when test="request.sortBy.name() == 'SKU_ASC'">
                        ORDER BY sku ASC
                    </when>
                </choose>
            </when>
        </choose>
        ) t
    </select>
</mapper>
