package com.knet.goods.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.enums.ProductMark;
import com.knet.goods.model.dto.req.ProductQueryRequest;
import com.knet.goods.model.dto.resp.ProductBySkuDtoResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/8 15:30
 * @description: 商品查询临时表优化测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class KnetProductServiceTempTableTest {

    @Resource
    private IKnetProductService knetProductService;

    @Resource
    private ISkuCacheService skuCacheService;

    /**
     * 测试临时表优化效果
     * 模拟大量SKU查询场景
     */
    @Test
    public void testTempTableOptimization() {
        // 准备测试数据
        ProductQueryRequest request = new ProductQueryRequest();
        request.setPageNo(1);
        request.setPageSize(20);
        request.setSku("nike"); // 使用一个通用的SKU关键词，可能匹配大量数据
        request.setMark(ProductMark.ALL);

        log.info("开始测试临时表优化效果");
        
        // 执行查询并记录性能
        StopWatch stopWatch = new StopWatch("临时表优化测试");
        stopWatch.start("查询商品列表");
        
        IPage<ProductBySkuDtoResp> result = knetProductService.queryProductGroupBySku(request);
        
        stopWatch.stop();
        
        log.info("查询完成，总耗时: {} ms", stopWatch.getTotalTimeMillis());
        log.info("查询结果 - 总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());
        
        // 输出前几条记录用于验证
        if (!result.getRecords().isEmpty()) {
            log.info("前3条查询结果:");
            result.getRecords().stream().limit(3).forEach(product -> {
                log.info("SKU: {}, 品牌: {}, 价格区间: ${}-${}, 数量: {}", 
                    product.getSku(), 
                    product.getBrand(), 
                    product.getMinPrice(), 
                    product.getMaxPrice(), 
                    product.getTotal());
            });
        }
    }

    /**
     * 测试SKU缓存匹配性能
     */
    @Test
    public void testSkuCachePerformance() {
        String[] testKeywords = {"nike", "adidas", "jordan", "air", "max"};
        
        for (String keyword : testKeywords) {
            StopWatch stopWatch = new StopWatch("SKU缓存测试-" + keyword);
            stopWatch.start("匹配SKU");
            
            var skus = skuCacheService.matchSkus(keyword);
            var remarks = skuCacheService.matchProductsByRemark(keyword);
            
            stopWatch.stop();
            
            log.info("关键词: {}, SKU匹配数: {}, 备注匹配数: {}, 耗时: {} ms", 
                keyword, skus.size(), remarks.size(), stopWatch.getTotalTimeMillis());
        }
    }

    /**
     * 对比测试：强制使用传统方式 vs 临时表优化
     */
    @Test
    public void testPerformanceComparison() {
        ProductQueryRequest request = new ProductQueryRequest();
        request.setPageNo(1);
        request.setPageSize(20);
        request.setSku("air"); // 使用可能匹配大量数据的关键词
        request.setMark(ProductMark.ALL);

        log.info("开始性能对比测试");
        
        // 测试多次取平均值
        int testRounds = 3;
        long totalTime = 0;
        
        for (int i = 0; i < testRounds; i++) {
            StopWatch stopWatch = new StopWatch("性能测试-第" + (i + 1) + "轮");
            stopWatch.start("查询");
            
            IPage<ProductBySkuDtoResp> result = knetProductService.queryProductGroupBySku(request);
            
            stopWatch.stop();
            long currentTime = stopWatch.getTotalTimeMillis();
            totalTime += currentTime;
            
            log.info("第{}轮测试 - 耗时: {} ms, 结果数: {}", i + 1, currentTime, result.getTotal());
        }
        
        long avgTime = totalTime / testRounds;
        log.info("平均查询耗时: {} ms", avgTime);
    }
}
