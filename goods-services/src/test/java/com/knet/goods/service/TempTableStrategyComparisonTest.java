package com.knet.goods.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.enums.ProductMark;
import com.knet.goods.model.dto.req.ProductQueryRequest;
import com.knet.goods.model.dto.resp.ProductBySkuDtoResp;
import com.knet.goods.system.config.TempTableConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/8 17:30
 * @description: 临时表策略性能对比测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class TempTableStrategyComparisonTest {

    @Resource
    private IKnetProductService knetProductService;

    @Resource
    private ISkuCacheService skuCacheService;

    @Resource
    private TempTableConfig tempTableConfig;

    /**
     * 对比测试：单表策略 vs 分表策略
     */
    @Test
    public void testStrategyComparison() {
        // 准备测试数据
        String[] testKeywords = {"nike", "air", "jordan", "max", "adidas"};

        log.info("开始临时表策略性能对比测试");
        log.info("当前配置: {}", getConfigInfo());

        for (String keyword : testKeywords) {
            log.info("\n========== 测试关键词: {} ==========", keyword);

            // 预先获取匹配数据量
            var skus = skuCacheService.matchSkus(keyword);
            var remarks = skuCacheService.matchProductsByRemark(keyword);
            int totalCount = skus.size() + remarks.size();

            log.info("匹配数据量 - SKU: {}, 备注: {}, 总计: {}", skus.size(), remarks.size(), totalCount);

            if (totalCount < tempTableConfig.getSkuThreshold()) {
                log.info("数据量小于阈值，将使用传统查询，跳过策略对比");
                continue;
            }

            // 测试单表策略
            testSingleTableStrategy(keyword);

            // 测试分表策略
            testSeparateTablesStrategy(keyword);
        }
    }

    /**
     * 测试单表策略
     */
    private void testSingleTableStrategy(String keyword) {
        // 临时切换到单表策略
        TempTableConfig.Strategy originalStrategy = tempTableConfig.getStrategy();
        tempTableConfig.setStrategy(TempTableConfig.Strategy.SINGLE_TABLE);

        try {
            log.info("--- 测试单表策略 ---");

            List<Long> executionTimes = new ArrayList<>();
            int testRounds = 3;

            for (int i = 0; i < testRounds; i++) {
                StopWatch stopWatch = new StopWatch("单表策略-第" + (i + 1) + "轮");
                stopWatch.start("查询");

                ProductQueryRequest request = createTestRequest(keyword);
                IPage<ProductBySkuDtoResp> result = knetProductService.queryProductGroupBySku(request);

                stopWatch.stop();
                long currentTime = stopWatch.getTotalTimeMillis();
                executionTimes.add(currentTime);

                log.info("第{}轮 - 耗时: {} ms, 结果数: {}", i + 1, currentTime, result.getTotal());
            }

            long avgTime = executionTimes.stream().mapToLong(Long::longValue).sum() / testRounds;
            log.info("单表策略平均耗时: {} ms", avgTime);

        } finally {
            // 恢复原始策略
            tempTableConfig.setStrategy(originalStrategy);
        }
    }

    /**
     * 测试分表策略
     */
    private void testSeparateTablesStrategy(String keyword) {
        // 临时切换到分表策略
        TempTableConfig.Strategy originalStrategy = tempTableConfig.getStrategy();
        tempTableConfig.setStrategy(TempTableConfig.Strategy.SEPARATE_TABLES);

        try {
            log.info("--- 测试分表策略 ---");

            List<Long> executionTimes = new ArrayList<>();
            int testRounds = 3;

            for (int i = 0; i < testRounds; i++) {
                StopWatch stopWatch = new StopWatch("分表策略-第" + (i + 1) + "轮");
                stopWatch.start("查询");

                ProductQueryRequest request = createTestRequest(keyword);
                IPage<ProductBySkuDtoResp> result = knetProductService.queryProductGroupBySku(request);

                stopWatch.stop();
                long currentTime = stopWatch.getTotalTimeMillis();
                executionTimes.add(currentTime);

                log.info("第{}轮 - 耗时: {} ms, 结果数: {}", i + 1, currentTime, result.getTotal());
            }

            long avgTime = executionTimes.stream().mapToLong(Long::longValue).sum() / testRounds;
            log.info("分表策略平均耗时: {} ms", avgTime);

        } finally {
            // 恢复原始策略
            tempTableConfig.setStrategy(originalStrategy);
        }
    }

    /**
     * 创建测试请求
     */
    private ProductQueryRequest createTestRequest(String keyword) {
        ProductQueryRequest request = new ProductQueryRequest();
        request.setPageNo(1);
        request.setPageSize(20);
        request.setSku(keyword);
        request.setMark(ProductMark.ALL);
        return request;
    }

    /**
     * 获取配置信息
     */
    private String getConfigInfo() {
        return String.format("策略: %s, 启用: %s, 阈值: %d, 批次大小: %d",
                tempTableConfig.getStrategy(),
                tempTableConfig.isEnabled(),
                tempTableConfig.getSkuThreshold(),
                tempTableConfig.getBatchSize());
    }

    /**
     * 压力测试：大数据量场景
     */
    @Test
    public void testLargeDatasetPerformance() {
        log.info("开始大数据量压力测试");

        // 使用可能匹配大量数据的关键词
        String[] heavyKeywords = {"a", "e", "i", "o", "u"}; // 元音字母通常匹配更多数据

        for (String keyword : heavyKeywords) {
            var skus = skuCacheService.matchSkus(keyword);
            var remarks = skuCacheService.matchProductsByRemark(keyword);
            int totalCount = skus.size() + remarks.size();

            if (totalCount > 5000) { // 只测试大数据量场景
                log.info("测试关键词: {}, 数据量: {}", keyword, totalCount);

                // 测试分表策略在大数据量下的表现
                tempTableConfig.setStrategy(TempTableConfig.Strategy.SEPARATE_TABLES);

                StopWatch stopWatch = new StopWatch("大数据量测试-" + keyword);
                stopWatch.start("查询");

                ProductQueryRequest request = createTestRequest(keyword);
                IPage<ProductBySkuDtoResp> result = knetProductService.queryProductGroupBySku(request);

                stopWatch.stop();

                log.info("大数据量测试结果 - 关键词: {}, 数据量: {}, 耗时: {} ms, 结果数: {}",
                        keyword, totalCount, stopWatch.getTotalTimeMillis(), result.getTotal());

                break; // 只测试一个大数据量场景
            }
        }
    }

    /**
     * 配置切换测试
     */
    @Test
    public void testConfigurationSwitching() {
        log.info("测试配置动态切换功能");

        String testKeyword = "nike";
        ProductQueryRequest request = createTestRequest(testKeyword);

        // 测试单表策略
        tempTableConfig.setStrategy(TempTableConfig.Strategy.SINGLE_TABLE);
        log.info("当前策略: {}", tempTableConfig.getStrategy());

        IPage<ProductBySkuDtoResp> result1 = knetProductService.queryProductGroupBySku(request);
        log.info("单表策略结果数: {}", result1.getTotal());

        // 切换到分表策略
        tempTableConfig.setStrategy(TempTableConfig.Strategy.SEPARATE_TABLES);
        log.info("当前策略: {}", tempTableConfig.getStrategy());

        IPage<ProductBySkuDtoResp> result2 = knetProductService.queryProductGroupBySku(request);
        log.info("分表策略结果数: {}", result2.getTotal());

        // 验证结果一致性
        if (result1.getTotal().equals(result2.getTotal())) {
            log.info("✅ 策略切换测试通过，两种策略结果一致");
        } else {
            log.error("❌ 策略切换测试失败，结果不一致: 单表={}, 分表={}",
                    result1.getTotal(), result2.getTotal());
        }
    }
}
