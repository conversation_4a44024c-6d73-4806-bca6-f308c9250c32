package com.knet.notification;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/6/ 10:15
 * @description: 通知服务 主启动类
 */
@EnableRetry
@ComponentScan(basePackages = {"com.knet.notification", "com.knet.common"})
@EnableScheduling
@EnableAsync
@MapperScan("com.knet.notification.mapper")
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class NotificationServicesApplication {
    public static void main(String[] args) {
        System.setProperty("spring.main.allow-circular-references", "true");
        //禁用Log4j JNDI
        System.setProperty("log4j2.formatMsgNoLookups", "true");
        SpringApplication.run(NotificationServicesApplication.class);
        System.out.println(" 🚀Notification Service started successfully!");
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}