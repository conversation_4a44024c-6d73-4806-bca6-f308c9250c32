package com.knet.payment;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/3/12 15:10
 * @description: 支付服务 主启动类
 */
@ComponentScan(basePackages = {"com.knet.payment", "com.knet.common"})
@MapperScan("com.knet.payment.mapper")
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class PaymentServicesApplication {
    public static void main(String[] args) {
        System.setProperty("spring.main.allow-circular-references", "true");
        //禁用Log4j JNDI
        System.setProperty("log4j2.formatMsgNoLookups", "true");
        SpringApplication.run(PaymentServicesApplication.class);
        System.out.println(" 🚀 Payment Service started successfully!");
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
